# 数学物理方法知识点分类体系

## 概述

本分类体系基于2012-2024年数学物理方法期末考试试题的全面分析，涵盖了所有出现的知识点和理论概念。分类体系采用层次化结构，便于后续题目标注和统计分析。

## 一级分类

### 1. 偏微分方程基础理论 (PDE_THEORY)

#### 1.1 偏微分方程分类 (PDE_CLASSIFICATION)
- **1.1.1** 二阶线性偏微分方程类型判别 (PDE_TYPE_CLASSIFICATION)
  - 双曲型方程 (HYPERBOLIC_PDE)
  - 抛物型方程 (PARABOLIC_PDE) 
  - 椭圆型方程 (ELLIPTIC_PDE)
  - 混合型方程 (MIXED_PDE)

- **1.1.2** 线性与非线性方程 (LINEAR_NONLINEAR)
  - 线性偏微分方程 (LINEAR_PDE)
  - 非线性偏微分方程 (NONLINEAR_PDE)
  - 齐次与非齐次方程 (HOMOGENEOUS_NONHOMOGENEOUS)

#### 1.2 定解问题 (BOUNDARY_VALUE_PROBLEMS)
- **1.2.1** 定解问题的适定性 (WELL_POSEDNESS)
  - 解的存在性 (EXISTENCE)
  - 解的唯一性 (UNIQUENESS)
  - 解的稳定性 (STABILITY)

- **1.2.2** 边界条件类型 (BOUNDARY_CONDITIONS)
  - 第一类边界条件 (Dirichlet条件) (DIRICHLET_BC)
  - 第二类边界条件 (Neumann条件) (NEUMANN_BC)
  - 第三类边界条件 (Robin条件) (ROBIN_BC)
  - 自由边界条件 (FREE_BC)
  - 固定边界条件 (FIXED_BC)
  - 弹性支承边界条件 (ELASTIC_BC)

- **1.2.3** 初始条件 (INITIAL_CONDITIONS)
  - 初始位移条件 (INITIAL_DISPLACEMENT)
  - 初始速度条件 (INITIAL_VELOCITY)
  - 初始温度条件 (INITIAL_TEMPERATURE)

### 2. 波动方程 (WAVE_EQUATION)

#### 2.1 一维波动方程 (1D_WAVE)
- **2.1.1** 弦振动方程 (STRING_VIBRATION)
  - 标准波动方程 $u_{tt} = a^2 u_{xx}$ (STANDARD_WAVE_EQ)
  - 非齐次波动方程 $u_{tt} = a^2 u_{xx} + f(x,t)$ (NONHOMOGENEOUS_WAVE)
  - 弦的边界条件 (STRING_BC)

- **2.1.2** 达朗贝尔解法 (D_ALEMBERT_METHOD)
  - 达朗贝尔公式 (D_ALEMBERT_FORMULA)
  - 行波解 (TRAVELING_WAVE)
  - 左行波与右行波 (LEFT_RIGHT_WAVES)
  - 依赖区间 (DEPENDENCE_INTERVAL)
  - 决定区域 (DETERMINATION_DOMAIN)
  - 影响区域 (INFLUENCE_DOMAIN)

- **2.1.3** 半无界问题 (SEMI_INFINITE_WAVE)
  - 延拓法 (EXTENSION_METHOD)
  - 奇延拓 (ODD_EXTENSION)
  - 偶延拓 (EVEN_EXTENSION)

#### 2.2 高维波动方程 (HIGHER_DIM_WAVE)
- **2.2.1** 二维波动方程 (2D_WAVE)
  - 惠更斯原理 (HUYGENS_PRINCIPLE)
  - 无后效现象 (NO_AFTEREFFECT)

- **2.2.2** 三维波动方程 (3D_WAVE)
  - 弥散现象 (DISPERSION)
  - 后效现象 (AFTEREFFECT)

#### 2.3 古尔萨问题 (GOURSAT_PROBLEM)
- **2.3.1** 特征线方法 (CHARACTERISTIC_METHOD)
- **2.3.2** 变量替换 (VARIABLE_TRANSFORMATION)

### 3. 热传导方程 (HEAT_EQUATION)

#### 3.1 一维热传导方程 (1D_HEAT)
- **3.1.1** 扩散方程 (DIFFUSION_EQUATION)
  - 标准热传导方程 $u_t = a^2 u_{xx}$ (STANDARD_HEAT_EQ)
  - 非齐次热传导方程 $u_t = a^2 u_{xx} + f(x,t)$ (NONHOMOGENEOUS_HEAT)

- **3.1.2** 热传导边界条件 (HEAT_BC)
  - 温度边界条件 (TEMPERATURE_BC)
  - 绝热边界条件 (INSULATION_BC)
  - 热交换边界条件 (HEAT_EXCHANGE_BC)

- **3.1.3** 泊松公式 (POISSON_FORMULA)
  - 热传导方程初值问题解 (HEAT_INITIAL_VALUE_SOLUTION)
  - 基本解 (FUNDAMENTAL_SOLUTION)

#### 3.2 齐次化原理 (HOMOGENIZATION_PRINCIPLE)
- **3.2.1** 杜阿梅尔原则 (DUHAMEL_PRINCIPLE)
  - 热传导问题的齐次化 (HEAT_HOMOGENIZATION)
  - 波动问题的齐次化 (WAVE_HOMOGENIZATION)

#### 3.3 半无界热传导问题 (SEMI_INFINITE_HEAT)
- **3.3.1** 延拓法求解 (EXTENSION_METHOD_HEAT)

### 4. 拉普拉斯方程和泊松方程 (LAPLACE_POISSON)

#### 4.1 拉普拉斯方程 (LAPLACE_EQUATION)
- **4.1.1** 二维拉普拉斯方程 (2D_LAPLACE)
  - 直角坐标形式 $u_{xx} + u_{yy} = 0$ (CARTESIAN_LAPLACE_2D)
  - 极坐标形式 (POLAR_LAPLACE_2D)

- **4.1.2** 三维拉普拉斯方程 (3D_LAPLACE)
  - $u_{xx} + u_{yy} + u_{zz} = 0$ (CARTESIAN_LAPLACE_3D)

- **4.1.3** 调和函数 (HARMONIC_FUNCTIONS)
  - 调和函数性质 (HARMONIC_PROPERTIES)
  - 基本解 (FUNDAMENTAL_SOLUTIONS)
    - 二维基本解 $\ln r$ (2D_FUNDAMENTAL)
    - 三维基本解 $\frac{1}{r}$ (3D_FUNDAMENTAL)

#### 4.2 边值问题 (BOUNDARY_VALUE_PROBLEMS_LAPLACE)
- **4.2.1** Dirichlet问题 (DIRICHLET_PROBLEM)
  - 内问题 (INTERIOR_DIRICHLET)
  - 外问题 (EXTERIOR_DIRICHLET)

- **4.2.2** Neumann问题 (NEUMANN_PROBLEM)
  - 内问题 (INTERIOR_NEUMANN)
  - 外问题 (EXTERIOR_NEUMANN)

#### 4.3 泊松方程 (POISSON_EQUATION)
- **4.3.1** $u_{xx} + u_{yy} = f(x,y)$ (POISSON_2D)
- **4.3.2** $u_{xx} + u_{yy} + u_{zz} = f(x,y,z)$ (POISSON_3D)

### 5. 特殊函数与变换 (SPECIAL_FUNCTIONS_TRANSFORMS)

#### 5.1 δ函数 (DELTA_FUNCTION)
- **5.1.1** δ函数定义 (DELTA_DEFINITION)
- **5.1.2** δ函数性质 (DELTA_PROPERTIES)
- **5.1.3** δ函数积分计算 (DELTA_INTEGRATION)

#### 5.2 傅里叶变换 (FOURIER_TRANSFORM)
- **5.2.1** 傅里叶变换定义 (FOURIER_DEFINITION)
  - 正变换 $F(\lambda) = \int_{-\infty}^{+\infty} f(x) e^{-i\lambda x} dx$ (FOURIER_FORWARD)
  - 逆变换 (FOURIER_INVERSE)

- **5.2.2** 傅里叶变换性质 (FOURIER_PROPERTIES)
  - 线性性质 (FOURIER_LINEARITY)
  - 位移性质 (FOURIER_SHIFT)
  - 微分性质 (FOURIER_DERIVATIVE)
  - 相似性质 (FOURIER_SCALING)
  - 卷积定理 (FOURIER_CONVOLUTION)

- **5.2.3** 傅里叶变换应用 (FOURIER_APPLICATIONS)
  - 求解偏微分方程 (FOURIER_PDE_SOLVING)

#### 5.3 拉普拉斯变换 (LAPLACE_TRANSFORM)
- **5.3.1** 拉普拉斯变换定义 (LAPLACE_DEFINITION)
  - 正变换 $F(p) = \int_0^{+\infty} f(t) e^{-pt} dt$ (LAPLACE_FORWARD)
  - 逆变换 (LAPLACE_INVERSE)

- **5.3.2** 拉普拉斯变换性质 (LAPLACE_PROPERTIES)
  - 线性性质 (LAPLACE_LINEARITY)
  - 相似定理 (LAPLACE_SCALING)
  - 微分性质 (LAPLACE_DERIVATIVE)
  - 积分性质 (LAPLACE_INTEGRATION)
  - 卷积定理 (LAPLACE_CONVOLUTION)

- **5.3.3** 拉普拉斯变换应用 (LAPLACE_APPLICATIONS)
  - 常微分方程求解 (LAPLACE_ODE_SOLVING)
  - 积分方程求解 (LAPLACE_INTEGRAL_EQ)

#### 5.4 卷积 (CONVOLUTION)
- **5.4.1** 卷积定义 (CONVOLUTION_DEFINITION)
- **5.4.2** 卷积性质 (CONVOLUTION_PROPERTIES)
  - 可交换性 (CONVOLUTION_COMMUTATIVE)
  - 结合性 (CONVOLUTION_ASSOCIATIVE)

### 6. 求解方法 (SOLUTION_METHODS)

#### 6.1 分离变量法 (SEPARATION_OF_VARIABLES)
- **6.1.1** 特征值问题 (EIGENVALUE_PROBLEMS)
  - 特征值求解 (EIGENVALUE_CALCULATION)
  - 特征函数求解 (EIGENFUNCTION_CALCULATION)
  - 正交性 (ORTHOGONALITY)

- **6.1.2** 傅里叶级数解 (FOURIER_SERIES_SOLUTION)
  - 傅里叶系数确定 (FOURIER_COEFFICIENTS)
  - 级数收敛性 (SERIES_CONVERGENCE)

- **6.1.3** 分离变量法应用 (SEPARATION_APPLICATIONS)
  - 波动方程分离变量 (WAVE_SEPARATION)
  - 热传导方程分离变量 (HEAT_SEPARATION)
  - 拉普拉斯方程分离变量 (LAPLACE_SEPARATION)

#### 6.2 积分变换法 (INTEGRAL_TRANSFORM_METHOD)
- **6.2.1** 傅里叶变换法 (FOURIER_TRANSFORM_METHOD)
- **6.2.2** 拉普拉斯变换法 (LAPLACE_TRANSFORM_METHOD)

#### 6.3 行波法 (TRAVELING_WAVE_METHOD)
- **6.3.1** 达朗贝尔方法 (D_ALEMBERT_WAVE_METHOD)
- **6.3.2** 变量替换技巧 (VARIABLE_SUBSTITUTION)

#### 6.4 延拓法 (EXTENSION_METHOD)
- **6.4.1** 奇偶延拓 (ODD_EVEN_EXTENSION)
- **6.4.2** 半无界问题处理 (SEMI_INFINITE_TREATMENT)

### 7. 物理应用与建模 (PHYSICAL_APPLICATIONS)

#### 7.1 弦振动问题 (STRING_VIBRATION_PROBLEMS)
- **7.1.1** 弦的物理模型 (STRING_PHYSICAL_MODEL)
- **7.1.2** 边界条件的物理意义 (STRING_BC_PHYSICS)

#### 7.2 热传导问题 (HEAT_CONDUCTION_PROBLEMS)
- **7.2.1** 热传导物理模型 (HEAT_PHYSICAL_MODEL)
- **7.2.2** 热交换边界条件推导 (HEAT_EXCHANGE_DERIVATION)

#### 7.3 电路问题 (CIRCUIT_PROBLEMS)
- **7.3.1** RL串联电路 (RL_CIRCUIT)
- **7.3.2** 交流电路分析 (AC_CIRCUIT_ANALYSIS)

## 编码规则

每个知识点都有唯一的编码，格式为：`主类别_子类别_具体知识点`

例如：
- `PDE_THEORY_CLASSIFICATION_HYPERBOLIC` - 双曲型偏微分方程
- `WAVE_EQUATION_D_ALEMBERT_FORMULA` - 达朗贝尔公式
- `FOURIER_TRANSFORM_PROPERTIES_CONVOLUTION` - 傅里叶变换卷积定理

## 使用说明

1. **题目标注**：每道题目可标注一个或多个知识点编码
2. **难度等级**：可为每个知识点设置难度等级（基础/中等/困难）
3. **重要程度**：根据出现频率和分值确定重要程度
4. **关联性**：记录知识点之间的关联关系

## 更新维护

本分类体系基于2012-2024年试题分析建立，后续可根据新的试题内容进行扩展和完善。每次更新需要：

1. 检查新增知识点
2. 验证现有分类的完整性
3. 调整编码规则（如有必要）
4. 更新使用说明

---

**创建时间**：2024年
**版本**：v1.0
**覆盖年份**：2012-2024年
**总知识点数**：约150个细分知识点
